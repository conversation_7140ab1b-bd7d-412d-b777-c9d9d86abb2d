_format_version: "3.0"
_transform: true

services:
- name: ai-gw
  url: http://host.docker.internal:8086
  routes:
  - name: chat
    paths:
    - /ai/chat
  - name: images
    paths:
    - /ai/images
- name: ai-gw-realtime
  url: http://host.docker.internal:8086
  protocol: ws, wss
  routes:
  - name: realtime
    paths:
    - /ai/realtime
    protocols:
    - ws
    - wss
    
plugins:
- name: ai-proxy-advanced
  route: images
  config:
    genai_category: image/generation
    targets:
    - route_type: image/v1/images/generations
      auth:
          header_name: Authorization
          header_value: "Bearer ***************************************************"
      model:
        provider: "openai"
        name: "dall-e-3"

- name: ai-proxy-advanced
  route: realtime
  config:
    genai_category: realtime/generation
    targets:
    - route_type: "realtime/v1/realtime"
      auth:
          header_name: Authorization
          header_value: "Bearer ***************************************************"
      model:
        provider: "openai"
        name: "gpt-4o-realtime-preview-2024-12-17"
        options:
          max_tokens: 512
          temperature: 1.0        

- name: ai-proxy
  route: chat
  config:
    route_type: "llm/v1/chat"
    auth:
        header_name: Authorization
        header_value: "Bearer ***************************************************"
    model:
      provider: "openai"
      name: "gpt-4"
      # name: "qwen2.5:0.5b-instruct"
      options:
        max_tokens: 1024
        temperature: 0.5
        # upstream_url: http://host.docker.internal:8086/v1/chat/completions

- name: ai-prompt-compressor
  route: chat
  config:
    compressor_type: target_token
    compressor_url: http://kong-compressor:8080
    log_text_data: true
    stop_on_error: true
    compression_ranges:
    - min_tokens: 1
      max_tokens: 50
      value: 25
    - min_tokens: 50
      max_tokens: 100
      value: 75
    - min_tokens: 100
      max_tokens: 1000
      value: 100

- name: cors
  config:
    origins:
    - "*"
    methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
    headers:
    - "*"
    max_age: 3600
    credentials: true
