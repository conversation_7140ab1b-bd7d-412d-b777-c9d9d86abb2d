_format_version: "3.0"
_transform: true

services:
- name: ai-gw
  url: http://host.docker.internal:8086
  routes:
  - name: images
    paths:
    - /ai/images
    
plugins:
- name: ai-proxy-advanced
  route: images
  config:
    genai_category: image/generation
    targets:
    - route_type: image/v1/images/generations
      auth:
          header_name: Authorization
          header_value: "Bearer ***************************************************"
      model:
        provider: "openai"
        name: "dall-e-3"

- name: cors
  config:
    origins:
    - "*"
    methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
    headers:
    - "*"
    max_age: 3600
    credentials: true
