name: kong-event-gw-dev
services:
  kong:
    image: kong/kong-gateway-dev:3.11.0.0-rc.9-20250701-ubuntu
    container_name: kong-ee
    ports:
      - "8000-8002:8000-8002"
      - "8443:8443"
    volumes:
        - ./kong-config/:/kong/declarative/
    env_file:
      - ee.env
    environment:
        - KONG_DATABASE=off
        - KONG_DECLARATIVE_CONFIG=/kong/declarative/kong.yaml
        - KONG_PROXY_ACCESS_LOG=/dev/stdout
        - KONG_ADMIN_ACCESS_LOG=/dev/stdout
        - KONG_PROXY_ERROR_LOG=/dev/stderr
        - KONG_ADMIN_ERROR_LOG=/dev/stderr
        - KONG_ADMIN_LISTEN=0.0.0.0:8001
        - KONG_ADMIN_GUI_URL=http://localhost:8002
        - KONG_LOG_LEVEL=debug
        # - KONG_LICENSE_DATA